#[cfg(test)]
mod tests {
    use crate::VirtualWorkspace;

    #[test]
    fn test_basic_flow_analysis() {
        let mut ws = VirtualWorkspace::new();
        
        ws.def(
            r#"
local a = 1
if a then
    local b = 2
end
            "#,
        );

        // This test just ensures the flow analysis doesn't crash
        // More detailed tests can be added later
        assert!(true);
    }

    #[test]
    fn test_flow_with_conditions() {
        let mut ws = VirtualWorkspace::new();
        
        ws.def(
            r#"
            local x = nil
            if x then
                print("x is truthy")
            else
                print("x is falsy")
            end
            "#,
        );

        // This test just ensures the flow analysis doesn't crash
        assert!(true);
    }

    #[test]
    fn test_flow_with_loops() {
        let mut ws = VirtualWorkspace::new();
        
        ws.def(
            r#"
            for i = 1, 10 do
                if i == 5 then
                    break
                end
                print(i)
            end
            "#,
        );

        // This test just ensures the flow analysis doesn't crash
        assert!(true);
    }
}
