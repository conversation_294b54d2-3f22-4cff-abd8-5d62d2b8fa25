use std::sync::Arc;

use emmylua_parser::{LuaAssignStat, LuaAstPtr, LuaCallExpr, LuaDocTagCast, LuaExpr, LuaForStat, LuaNameExpr, LuaSyntaxId};
use internment::ArcIntern;
use smol_str::SmolStr;

use crate::{LuaType, LuaVarRefId};

/// Unique identifier for flow nodes
#[derive(Debu<PERSON>, <PERSON>lone, Copy, PartialEq, Eq, Hash, Default)]
pub struct FlowId(pub u32);

/// Represents how flow nodes are connected
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum FlowAntecedent {
    /// Single predecessor node
    Single(FlowId),
    /// Multiple predecessor nodes (stored externally by index)
    Multiple(u32),
}

/// Main flow node structure containing all flow analysis information
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct FlowNode {
    pub id: FlowId,
    pub kind: FlowNodeKind,
    pub antecedent: Option<FlowAntecedent>,
}

/// Different types of flow nodes in the control flow graph
#[derive(Debu<PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum FlowNodeKind {
    /// Entry point of the flow
    Start,
    /// Unreachable code
    Unreachable,
    /// Label for branching (if/else, switch cases)
    BranchLabel,
    /// Label for loops (while, for, repeat)
    LoopLabel,
    /// Named label (goto target)
    NamedLabel(ArcIntern<SmolStr>),
    /// Variable assignment
    Assignment(LuaAstPtr<LuaAssignStat>),
    /// Conditional flow (type guards, existence checks)
    TrueCondition(LuaAstPtr<LuaExpr>),
    /// Conditional flow (type guards, existence checks)
    FalseCondition(LuaAstPtr<LuaExpr>),
    /// Call expression
    Call(LuaAstPtr<LuaCallExpr>),
    /// Variable reference
    Variable(LuaAstPtr<LuaNameExpr>),
    ForIStat(LuaAstPtr<LuaForStat>),
    TagCast(LuaAstPtr<LuaDocTagCast>),
    /// Type assertion or flow condition
    Assertion(ArcIntern<FlowAssertion>),
    /// Break statement
    Break,
    /// Return statement
    Return,
}

#[allow(unused)]
impl FlowNodeKind {
    pub fn is_branch_label(&self) -> bool {
        matches!(self, FlowNodeKind::BranchLabel)
    }

    pub fn is_loop_label(&self) -> bool {
        matches!(self, FlowNodeKind::LoopLabel)
    }

    pub fn is_named_label(&self) -> bool {
        matches!(self, FlowNodeKind::NamedLabel(_))
    }

    pub fn is_change_flow(&self) -> bool {
        matches!(self, FlowNodeKind::Break | FlowNodeKind::Return)
    }

    pub fn is_assignment(&self) -> bool {
        matches!(self, FlowNodeKind::Assignment(_))
    }

    pub fn is_conditional(&self) -> bool {
        matches!(self, FlowNodeKind::TrueCondition(_) | FlowNodeKind::FalseCondition(_))
    }

    pub fn is_unreachable(&self) -> bool {
        matches!(self, FlowNodeKind::Unreachable)
    }

    pub fn is_assertion(&self) -> bool {
        matches!(self, FlowNodeKind::Assertion(_))
    }
}

/// Flow assertion types for type narrowing and flow analysis
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum FlowAssertion {
    /// Variable is truthy (not nil or false)
    Truthy(LuaVarRefId),
    /// Variable is falsy (nil or false)
    Falsy(LuaVarRefId),
    /// Type guard assertion
    TypeGuard(LuaVarRefId, LuaType),
    /// Type force assertion (overrides type)
    TypeForce(LuaVarRefId, LuaType),
    /// Type narrowing assertion
    TypeNarrow(LuaVarRefId, LuaType),
    /// Type removal assertion
    TypeRemove(LuaVarRefId, LuaType),
    /// Call assertion (function call that returns boolean)
    TruthyCall(ArcIntern<FlowCall>),
    /// Negated call assertion
    FalsyCall(ArcIntern<FlowCall>),
    /// Combined assertions (AND)
    And(Arc<Vec<FlowAssertion>>),
    /// Alternative assertions (OR)
    Or(Arc<Vec<FlowAssertion>>),
}

impl FlowAssertion {
    /// Get the negation of this assertion
    pub fn get_negation(&self) -> FlowAssertion {
        match self {
            FlowAssertion::Truthy(var) => FlowAssertion::Falsy(var.clone()),
            FlowAssertion::Falsy(var) => FlowAssertion::Truthy(var.clone()),
            FlowAssertion::TypeGuard(var, ty) => FlowAssertion::TypeRemove(var.clone(), ty.clone()),
            FlowAssertion::TypeForce(var, ty) => FlowAssertion::TypeRemove(var.clone(), ty.clone()),
            FlowAssertion::TypeNarrow(var, ty) => FlowAssertion::TypeRemove(var.clone(), ty.clone()),
            FlowAssertion::TypeRemove(var, ty) => FlowAssertion::TypeNarrow(var.clone(), ty.clone()),
            FlowAssertion::TruthyCall(call) => FlowAssertion::FalsyCall(call.clone()),
            FlowAssertion::FalsyCall(call) => FlowAssertion::TruthyCall(call.clone()),
            FlowAssertion::And(assertions) => {
                let negated: Vec<_> = assertions.iter().map(|a| a.get_negation()).collect();
                FlowAssertion::Or(negated.into())
            }
            FlowAssertion::Or(assertions) => {
                let negated: Vec<_> = assertions.iter().map(|a| a.get_negation()).collect();
                FlowAssertion::And(negated.into())
            }
        }
    }

    /// Check if this assertion affects the given variable
    pub fn affects_variable(&self, var_ref: &LuaVarRefId) -> bool {
        match self {
            FlowAssertion::Truthy(var) | FlowAssertion::Falsy(var)
            | FlowAssertion::TypeGuard(var, _) | FlowAssertion::TypeForce(var, _)
            | FlowAssertion::TypeNarrow(var, _) | FlowAssertion::TypeRemove(var, _) => var == var_ref,
            FlowAssertion::TruthyCall(call) | FlowAssertion::FalsyCall(call) => {
                call.affects_variable(var_ref)
            }
            FlowAssertion::And(assertions) | FlowAssertion::Or(assertions) => {
                assertions.iter().any(|a| a.affects_variable(var_ref))
            }
        }
    }
}

/// Represents a function call in flow analysis
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct FlowCall {
    /// The call expression syntax ID
    pub call_expr: LuaSyntaxId,
    /// Arguments to the call
    pub args: Vec<LuaVarRefId>,
    /// Self reference if this is a method call
    pub self_var_ref: Option<LuaVarRefId>,
}

impl FlowCall {
    /// Check if this call affects the given variable
    pub fn affects_variable(&self, var_ref: &LuaVarRefId) -> bool {
        self.args.contains(var_ref) ||
        self.self_var_ref.as_ref().map_or(false, |self_var| self_var == var_ref)
    }
}
